package gormxx

import (
	appContext "contentmanager/library/context"
	"contentmanager/pkgs/reservation"
	"errors"
	"gorm.io/gorm"
	"reflect"
)

type ReservationPlugin struct{}

func (p ReservationPlugin) Name() string {
	return "modification_plugin"
}

func (p ReservationPlugin) Initialize(db *gorm.DB) error {
	if err := db.Callback().Create().Before("gorm:create").Register("modification:before_create", p.beforeCreate); err != nil {
		return err
	}

	if err := db.Callback().Update().Before("gorm:update").Register("modification:before_update", p.beforeUpdate); err != nil {
		return err
	}

	return nil
}

func (p ReservationPlugin) beforeCreate(db *gorm.DB) {
	p.modifyStruct(db)
}

func (p ReservationPlugin) beforeUpdate(db *gorm.DB) {
	p.modifyStruct(db)
}

func (p ReservationPlugin) modifyStruct(db *gorm.DB) {
	if db.Statement.Schema != nil {
		r, ok := db.Statement.Context.Value("app_context").(appContext.AppContextProvider)
		if !ok {
			db.AddError(errors.New("app_context not found in context"))
			return
		}

		// Example: Validation that can break the operation
		if err := p.shouldRejectOperation(r, db); err != nil {
			db.AddError(err)
			return // This stops the operation
		}

		// Your global modification logic here
		// Use reflection to modify fields across all models

		// Example: Set a tracking field
		//if field := db.Statement.Schema.LookUpField("UpdatedAt"); field != nil {
		//	if err := field.Set(db.Statement.Context, db.Statement.ReflectValue, time.Now()); err != nil {
		//		db.AddError(fmt.Errorf("failed to set UpdatedAt: %w", err))
		//		return
		//	}
		//}
	}
}

// Example validation method that can reject operations
func (p ReservationPlugin) shouldRejectOperation(r appContext.AppContextProvider, db *gorm.DB) error {
	// Example: Check if the model implements a specific interface
	// and validate business rules

	switch db.Statement.ReflectValue.Kind() {
	case reflect.Struct:
		// Example: Check if this is a reservation-related model
		if reservable, ok := db.Statement.ReflectValue.Interface().(reservation.IReservable); ok {
			r.Logger().Info().Msg(reservable.String())

			// APPROACH 1: Use AppContext's TenantDatabase() for additional queries
			// This is the recommended approach as it uses the same connection pool
			// and respects tenant isolation
			if err := p.validateWithAdditionalQuery(r, reservable); err != nil {
				return err
			}
		}
	case reflect.Slice, reflect.Array:
		// Handle batch operations
		for i := 0; i < db.Statement.ReflectValue.Len(); i++ {
			item := db.Statement.ReflectValue.Index(i)
			if reservable, ok := item.Interface().(reservation.IReservable); ok {
				r.Logger().Info().Msg(reservable.String())

				// Validate each item in batch operations
				if err := p.validateWithAdditionalQuery(r, reservable); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// Example method showing how to run additional queries
func (p ReservationPlugin) validateWithAdditionalQuery(r appContext.AppContextProvider, reservable reservation.IReservable) error {
	// APPROACH 1: Use AppContext's TenantDatabase() - RECOMMENDED
	// This uses the same connection pool and respects tenant boundaries
	tenantDB := r.TenantDatabase()

	// Example: Query another table to validate business rules
	var count int64
	if err := tenantDB.Table("some_validation_table").
		Where("related_id = ? AND status = ?", reservable.GetID(), "active").
		Count(&count).Error; err != nil {
		return fmt.Errorf("validation query failed: %w", err)
	}

	// Example business rule: reject if there are more than 5 active related records
	if count > 5 {
		return errors.New("operation rejected: too many active related records")
	}

	// Example: Query for specific validation data
	type ValidationRecord struct {
		ID     string `gorm:"column:id"`
		Status string `gorm:"column:status"`
		Value  int    `gorm:"column:value"`
	}

	var validationData ValidationRecord
	err := tenantDB.Table("validation_table").
		Where("entity_id = ?", reservable.GetID()).
		First(&validationData).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to fetch validation data: %w", err)
	}

	// If record exists, apply business rules
	if err == nil {
		if validationData.Status == "blocked" {
			return errors.New("operation rejected: entity is blocked")
		}
		if validationData.Value < 0 {
			return errors.New("operation rejected: invalid value")
		}
	}

	return nil
}

//// Register the plugin
//db.Use(ReservationPlugin{})
