import React, { useMemo } from 'react'
import { CustomMenuItem } from './CustomMenu'
import TimerOffIcon from '@mui/icons-material/TimerOff'
import TimerIcon from '@mui/icons-material/Timer'
import { notify } from '../../../helpers'
import { expireContentQuery } from '@/pkgs/content/queries'

export const ExpireToggleCustomMenuItem = ({ value, onChange, disabled }) => {
    const isExpired = useMemo(() => !!value?.expire_at && Date.parse(value?.expire_at) < Date.now(), [value])

    const toggleExpiration = async () => {
        try {
            await expireContentQuery(value?.ID || value?.id, value?.Workspace)
            onChange?.()
        } catch (e) {
            console.error(e)
            notify('Whoops - Something went wrong, find more details in the console.', 'error')
        }
    }

    if (value == null) {
        return null
    }
    return (
        <CustomMenuItem
            text={isExpired ? 'Un-expire' : 'Expire now'}
            onClick={() => toggleExpiration()}
            disabled={disabled}
        >
            {isExpired ? <TimerOffIcon fontSize='small' /> : <TimerIcon />}
        </CustomMenuItem>
    )
}
